local redis = require "resty.redis"

-- 获取客户端真实IP
local function get_client_ip()
    local ip = ngx.var.http_x_forwarded_for
    if ip then
        -- 取第一个IP (处理多级代理)
        ip = ip:match("([^,]+)")
    end
    
    if not ip or ip == "" then
        ip = ngx.var.http_x_real_ip
    end
    
    if not ip or ip == "" then
        ip = ngx.var.remote_addr
    end
    
    return ip
end

-- 创建Redis连接
local function get_redis_connection()
    local red = redis:new()
    red:set_timeout(_G.redis_config.timeout)
    
    local ok, err = red:connect(_G.redis_config.host, _G.redis_config.port)
    if not ok then
        ngx.log(ngx.ERR, "Failed to connect to Redis: ", err)
        return nil, err
    end
    
    return red, nil
end

-- 关闭Redis连接
local function close_redis_connection(red)
    if red then
        red:set_keepalive(_G.redis_config.keepalive_timeout, 
                         _G.redis_config.keepalive_pool_size)
    end
end

-- 检查是否为可信IP
local function is_trusted_ip(red, ip)
    local is_trusted, err = red:sismember("trusted_ips", ip)
    if err then
        ngx.log(ngx.ERR, "Redis sismember error: ", err)
        return false
    end
    return is_trusted == 1
end

-- 检查是否在黑名单
local function is_blacklisted_ip(red, ip)
    local is_blacklisted, err = red:sismember("blacklist_ip", ip)
    if err then
        ngx.log(ngx.ERR, "Redis sismember error: ", err)
        return false
    end
    return is_blacklisted == 1
end

-- URL分类检查
local function classify_url(uri)
    -- 检查黑名单URL
    for _, pattern in ipairs(_G.url_config.blacklist) do
        if ngx.re.match(uri, pattern, "jo") then
            return "blacklist"
        end
    end
    
    -- 检查敏感URL
    for _, pattern in ipairs(_G.url_config.sensitive) do
        if ngx.re.match(uri, pattern, "jo") then
            return "sensitive"
        end
    end
    
    -- 检查白名单URL
    for _, pattern in ipairs(_G.url_config.whitelist) do
        if ngx.re.match(uri, pattern, "jo") then
            return "whitelist"
        end
    end
    
    -- 检查静态资源URL
    for _, pattern in ipairs(_G.url_config.static) do
        if ngx.re.match(uri, pattern, "jo") then
            return "static"
        end
    end
    
    -- 检查必须访问URL
    for _, pattern in ipairs(_G.url_config.required) do
        if ngx.re.match(uri, pattern, "jo") then
            return "required"
        end
    end
    
    return "unknown"
end

-- 检查IP是否访问过必须URL
local function check_required_access(red, ip)
    local key = "ip_access:" .. ip
    local required, err = red:hget(key, "required")
    if err then
        ngx.log(ngx.ERR, "Redis hget error: ", err)
        return false
    end
    return required == "1"
end

-- 检查IP是否访问过静态资源
local function check_static_access(red, ip)
    local key = "ip_access:" .. ip
    local static, err = red:hget(key, "static")
    if err then
        ngx.log(ngx.ERR, "Redis hget error: ", err)
        return false
    end
    return static == "1"
end

-- 获取白名单URL访问次数
local function get_whitelist_count(red, ip)
    local key = "ip_access:" .. ip
    local count, err = red:hget(key, "white_count")
    if err then
        ngx.log(ngx.ERR, "Redis hget error: ", err)
        return 0
    end
    return tonumber(count) or 0
end

-- 更新IP访问记录
local function update_access_record(red, ip, url_type)
    local key = "ip_access:" .. ip
    
    if url_type == "static" then
        red:hset(key, "static", "1")
        red:hset(key, "required", "1")  -- 静态资源也算必须访问
    elseif url_type == "required" then
        red:hset(key, "required", "1")
    elseif url_type == "whitelist" then
        red:hincrby(key, "white_count", 1)
    end
    
    -- 设置过期时间
    red:expire(key, _G.defense_config.ip_record_ttl)
end

-- 拉黑IP
local function blacklist_ip(red, ip, reason)
    -- 添加到黑名单
    local ok, err = red:sadd("blacklist_ip", ip)
    if not ok then
        ngx.log(ngx.ERR, "Failed to add IP to blacklist: ", err)
    end
    
    -- 设置黑名单过期时间
    red:expire("blacklist_ip", _G.defense_config.blacklist_ttl)
    
    -- 记录日志
    ngx.log(ngx.WARN, "IP blocked: ", ip, ", reason: ", reason, ", URI: ", ngx.var.uri)
    
    -- 返回403响应
    ngx.status = 403
    ngx.header["Content-Type"] = "application/json; charset=utf-8"
    ngx.header["Cache-Control"] = "no-cache, no-store, must-revalidate"
    ngx.say('{"error":"Access denied","code":403,"message":"Your IP has been blocked due to suspicious activity"}')
    ngx.exit(403)
end

-- 主防御逻辑
local function cc_defense()
    local ip = get_client_ip()
    local uri = ngx.var.uri

    -- 获取Redis连接
    local red, err = get_redis_connection()
    if not red then
        ngx.log(ngx.ERR, "Redis connection failed, allowing request: ", err)
        return -- Redis连接失败时放行，避免影响正常服务
    end

    -- 检查是否为可信IP
    if is_trusted_ip(red, ip) then
        close_redis_connection(red)
        return -- 可信IP直接放行
    end

    -- 检查是否在黑名单
    if is_blacklisted_ip(red, ip) then
        close_redis_connection(red)
        ngx.status = 403
        ngx.header["Content-Type"] = "application/json; charset=utf-8"
        ngx.say('{"error":"IP blocked","code":403}')
        ngx.exit(403)
    end

    -- URL分类
    local url_type = classify_url(uri)

    -- 根据URL类型进行不同处理
    if url_type == "blacklist" then
        -- 访问黑名单URL直接拉黑
        blacklist_ip(red, ip, "访问黑名单URL")

    elseif url_type == "sensitive" then
        -- 访问敏感URL需要检查是否访问过必须URL
        if not check_required_access(red, ip) then
            blacklist_ip(red, ip, "访问敏感URL但未访问必须URL")
        end

    elseif url_type == "whitelist" then
        -- 白名单URL处理
        local white_count = get_whitelist_count(red, ip)

        if white_count >= _G.defense_config.whitelist_repeat_threshold then
            -- 非首次访问白名单URL，需要检查是否访问过静态资源
            if not check_static_access(red, ip) then
                blacklist_ip(red, ip, "重复访问白名单URL但未访问静态资源")
            end
        end

        -- 更新访问记录
        update_access_record(red, ip, "whitelist")

    elseif url_type == "static" then
        -- 访问静态资源，更新记录
        update_access_record(red, ip, "static")

    elseif url_type == "required" then
        -- 访问必须URL，更新记录
        update_access_record(red, ip, "required")

    end

    -- 关闭Redis连接
    close_redis_connection(red)
end

-- 执行防御逻辑，捕获异常
local function safe_cc_defense()
    local ok, err = pcall(cc_defense)
    if not ok then
        ngx.log(ngx.ERR, "CC defense error: ", err)
        -- 发生错误时放行请求，避免影响正常服务
    end
end

-- 执行
safe_cc_defense()
