# Nginx CC防御系统

基于Nginx + Lua + Redis的CC攻击防御系统，通过分析用户访问行为模式来识别和阻止恶意请求。

## 系统架构

- **Nginx + OpenResty**: 主要防御逻辑执行层
- **Redis**: 高性能数据存储，保存IP访问记录、黑名单、可信IP等
- **后端服务**: 负责将登录成功的IP写入Redis可信IP集合

## 防御策略

### 1. 可信IP机制
- 用户登录成功后，后端将IP添加到Redis的`trusted_ips`集合
- 可信IP的所有请求直接放行，无需检测

### 2. URL分类防御
- **黑名单URL**: 登录后才能访问的接口，未登录IP访问直接拉黑
- **敏感URL**: 需要检查是否访问过必须URL，未访问则拉黑
- **白名单URL**: 入口页面，首次访问放行，重复访问需检查静态资源访问
- **静态资源URL**: CSS/JS/图片等，访问后标记为已访问必须URL
- **必须访问URL**: 验证码、配置接口等，作为访问敏感URL的前置条件

### 3. 行为检测逻辑
1. **首次访问白名单URL** → 放行并记录
2. **重复访问白名单URL** → 检查是否访问过静态资源，未访问则拉黑
3. **访问敏感URL** → 检查是否访问过必须URL，未访问则拉黑
4. **访问黑名单URL** → 直接拉黑
5. **访问静态资源** → 标记为已访问必须URL

## 文件说明

### nginx.conf
Nginx主配置文件，配置了：
- Lua脚本路径
- 共享内存字典
- 后端服务代理
- CC防御模块加载

### init.lua
初始化配置文件，包含：
- URL分类规则配置
- Redis连接配置
- 防御参数配置

### cc_defense.lua
主防御逻辑文件，实现：
- IP获取和分类
- Redis连接管理
- URL分类检测
- 访问行为分析
- IP拉黑处理

## Redis数据结构

```
# 可信IP集合 (Set类型，永久存储)
trusted_ips -> {*************, *********, ...}

# IP访问记录 (Hash类型，TTL 2小时)
ip_access:************* -> {
    "static": "1",      # 是否访问过静态资源
    "required": "1",    # 是否访问过必须URL  
    "white_count": "3"  # 白名单URL访问次数
}

# IP黑名单 (Set类型，TTL 24小时)
blacklist_ip -> {*************, *********, ...}
```

## 部署步骤

### 1. 安装OpenResty
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install openresty

# CentOS/RHEL
sudo yum install openresty
```

### 2. 部署文件
```bash
# 创建Lua脚本目录
sudo mkdir -p /etc/nginx/lua

# 复制Lua脚本
sudo cp init.lua /etc/nginx/lua/
sudo cp cc_defense.lua /etc/nginx/lua/

# 设置权限
sudo chown -R nginx:nginx /etc/nginx/lua
sudo chmod 644 /etc/nginx/lua/*.lua

# 复制nginx配置
sudo cp nginx.conf /etc/nginx/nginx.conf
```

### 3. 配置Redis
确保Redis服务运行在127.0.0.1:6379，或修改init.lua中的Redis配置。

### 4. 后端集成
在用户登录成功后，后端需要将IP添加到Redis：

```python
import redis

def add_trusted_ip(user_ip):
    r = redis.Redis(host='127.0.0.1', port=6379, db=0)
    r.sadd('trusted_ips', user_ip)
```

### 5. 启动服务
```bash
# 测试配置
sudo nginx -t

# 重载配置
sudo nginx -s reload
```

## 监控和维护

### 查看Redis数据
```bash
# 查看可信IP数量
redis-cli scard trusted_ips

# 查看某个IP的访问记录
redis-cli hgetall ip_access:*************

# 查看黑名单IP
redis-cli smembers blacklist_ip

# 清空黑名单
redis-cli del blacklist_ip
```

### 日志监控
```bash
# 查看nginx错误日志
tail -f /var/log/nginx/error.log

# 查看被拦截的IP
grep "IP blocked" /var/log/nginx/error.log
```

## 配置调优

### URL规则调整
修改`init.lua`中的`url_config`来调整URL分类规则。

### 防御参数调整
修改`init.lua`中的`defense_config`来调整：
- IP记录过期时间
- 黑名单过期时间  
- 白名单重复访问阈值

### Redis连接优化
根据并发量调整`redis_config`中的连接池参数。

## 注意事项

1. **性能影响**: 每个请求都会进行Redis查询，建议使用高性能Redis实例
2. **误判处理**: 可以通过手动从黑名单中移除IP来处理误判
3. **规则调整**: 根据实际业务场景调整URL分类规则
4. **监控告警**: 建议设置监控来跟踪拦截情况和系统性能
