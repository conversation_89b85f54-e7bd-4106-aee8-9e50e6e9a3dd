#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端服务示例代码
演示如何在用户登录成功后将IP添加到Redis可信IP集合
"""

import redis
import time
from datetime import datetime

class CCDefenseManager:
    def __init__(self, redis_host='127.0.0.1', redis_port=6379, redis_db=0):
        """
        初始化CC防御管理器
        
        Args:
            redis_host: Redis主机地址
            redis_port: Redis端口
            redis_db: Redis数据库编号
        """
        self.redis_client = redis.Redis(
            host=redis_host, 
            port=redis_port, 
            db=redis_db,
            decode_responses=True
        )
    
    def add_trusted_ip(self, user_ip, user_id=None):
        """
        添加可信IP到Redis
        
        Args:
            user_ip: 用户IP地址
            user_id: 用户ID (可选)
        
        Returns:
            bool: 操作是否成功
        """
        try:
            # 添加到可信IP集合
            result = self.redis_client.sadd('trusted_ips', user_ip)
            
            # 可选：记录详细信息
            if user_id:
                detail_key = f'trusted_ip_detail:{user_ip}'
                self.redis_client.hset(detail_key, mapping={
                    'user_id': user_id,
                    'login_time': int(time.time()),
                    'created_at': datetime.now().isoformat(),
                    'status': 'active'
                })
                # 设置详细信息过期时间 (7天)
                self.redis_client.expire(detail_key, 7 * 24 * 3600)
            
            print(f"Successfully added trusted IP: {user_ip}")
            return True
            
        except Exception as e:
            print(f"Failed to add trusted IP {user_ip}: {e}")
            return False
    
    def remove_trusted_ip(self, user_ip):
        """
        从可信IP集合中移除IP
        
        Args:
            user_ip: 用户IP地址
        
        Returns:
            bool: 操作是否成功
        """
        try:
            # 从可信IP集合中移除
            result = self.redis_client.srem('trusted_ips', user_ip)
            
            # 删除详细信息
            detail_key = f'trusted_ip_detail:{user_ip}'
            self.redis_client.delete(detail_key)
            
            print(f"Successfully removed trusted IP: {user_ip}")
            return True
            
        except Exception as e:
            print(f"Failed to remove trusted IP {user_ip}: {e}")
            return False
    
    def is_trusted_ip(self, user_ip):
        """
        检查IP是否为可信IP
        
        Args:
            user_ip: 用户IP地址
        
        Returns:
            bool: 是否为可信IP
        """
        try:
            return self.redis_client.sismember('trusted_ips', user_ip)
        except Exception as e:
            print(f"Failed to check trusted IP {user_ip}: {e}")
            return False
    
    def get_trusted_ips(self):
        """
        获取所有可信IP
        
        Returns:
            set: 可信IP集合
        """
        try:
            return self.redis_client.smembers('trusted_ips')
        except Exception as e:
            print(f"Failed to get trusted IPs: {e}")
            return set()
    
    def remove_from_blacklist(self, user_ip):
        """
        从黑名单中移除IP (处理误判)
        
        Args:
            user_ip: 用户IP地址
        
        Returns:
            bool: 操作是否成功
        """
        try:
            result = self.redis_client.srem('blacklist_ip', user_ip)
            print(f"Successfully removed IP from blacklist: {user_ip}")
            return True
        except Exception as e:
            print(f"Failed to remove IP from blacklist {user_ip}: {e}")
            return False
    
    def get_blacklisted_ips(self):
        """
        获取所有黑名单IP
        
        Returns:
            set: 黑名单IP集合
        """
        try:
            return self.redis_client.smembers('blacklist_ip')
        except Exception as e:
            print(f"Failed to get blacklisted IPs: {e}")
            return set()
    
    def get_ip_access_record(self, user_ip):
        """
        获取IP的访问记录
        
        Args:
            user_ip: 用户IP地址
        
        Returns:
            dict: IP访问记录
        """
        try:
            key = f'ip_access:{user_ip}'
            return self.redis_client.hgetall(key)
        except Exception as e:
            print(f"Failed to get IP access record {user_ip}: {e}")
            return {}

# 使用示例
def example_usage():
    """使用示例"""
    
    # 初始化CC防御管理器
    cc_manager = CCDefenseManager()
    
    # 模拟用户登录成功
    user_ip = "*************"
    user_id = "user123"
    
    print("=== 用户登录成功处理 ===")
    success = cc_manager.add_trusted_ip(user_ip, user_id)
    if success:
        print(f"用户 {user_id} 的IP {user_ip} 已添加到可信列表")
    
    # 检查IP是否为可信IP
    print("\n=== 检查可信IP ===")
    is_trusted = cc_manager.is_trusted_ip(user_ip)
    print(f"IP {user_ip} 是否为可信IP: {is_trusted}")
    
    # 获取所有可信IP
    print("\n=== 所有可信IP ===")
    trusted_ips = cc_manager.get_trusted_ips()
    print(f"可信IP列表: {trusted_ips}")
    
    # 获取IP访问记录
    print("\n=== IP访问记录 ===")
    access_record = cc_manager.get_ip_access_record(user_ip)
    print(f"IP {user_ip} 访问记录: {access_record}")
    
    # 获取黑名单IP
    print("\n=== 黑名单IP ===")
    blacklisted_ips = cc_manager.get_blacklisted_ips()
    print(f"黑名单IP列表: {blacklisted_ips}")

if __name__ == "__main__":
    example_usage()
