-- URL分类配置
_G.url_config = {
    -- 黑名单URL (登录后才能访问的接口)
    blacklist = {
        "^/api/user/profile",
        "^/api/admin/.*",
        "^/api/order/.*",
        "^/api/dashboard/.*",
        "^/api/settings/.*",
        "^/api/management/.*"
    },
    
    -- 敏感URL (需要检查前置访问)
    sensitive = {
        "^/api/data/.*",
        "^/api/search",
        "^/api/submit",
        "^/api/upload",
        "^/api/download/.*",
        "^/api/export/.*"
    },
    
    -- 白名单URL (入口页面，首次访问放行)
    whitelist = {
        "^/$",
        "^/login$",
        "^/register$", 
        "^/about$",
        "^/contact$",
        "^/help$",
        "^/terms$",
        "^/privacy$"
    },
    
    -- 静态资源URL
    static = {
        ".*%.css$",
        ".*%.js$",
        ".*%.png$",
        ".*%.jpg$",
        ".*%.jpeg$",
        ".*%.gif$",
        ".*%.ico$",
        ".*%.svg$",
        ".*%.woff$",
        ".*%.woff2$",
        ".*%.ttf$",
        "^/static/.*",
        "^/assets/.*",
        "^/public/.*"
    },
    
    -- 必须访问URL (访问敏感URL前必须先访问这些)
    required = {
        "^/api/captcha",
        "^/api/config",
        "^/api/init",
        ".*%.css$",
        ".*%.js$"
    }
}

-- Redis配置
_G.redis_config = {
    host = "127.0.0.1",
    port = 6379,
    timeout = 1000,
    pool_size = 100,
    keepalive_timeout = 60000,
    keepalive_pool_size = 50
}

-- 防御配置
_G.defense_config = {
    -- IP访问记录过期时间 (秒)
    ip_record_ttl = 7200,  -- 2小时
    
    -- IP黑名单过期时间 (秒) 
    blacklist_ttl = 86400, -- 24小时
    
    -- 白名单URL重复访问检查阈值
    whitelist_repeat_threshold = 1
}

ngx.log(ngx.INFO, "CC Defense initialized")
