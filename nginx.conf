http {
    # Lua包路径
    lua_package_path "/etc/nginx/lua/?.lua;;";
    
    # 共享内存字典
    lua_shared_dict config_cache 10m;
    
    # 初始化脚本
    init_by_lua_file /etc/nginx/lua/init.lua;
    
    upstream backend {
        server 127.0.0.1:8080;
    }
    
    server {
        listen 80;
        server_name your-domain.com;
        
        # CC防御检查
        access_by_lua_file /etc/nginx/lua/cc_defense.lua;
        
        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
