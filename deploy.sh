#!/bin/bash

# Nginx CC防御系统部署脚本

set -e

echo "=== Nginx CC防御系统部署脚本 ==="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "无法检测操作系统版本"
    exit 1
fi

echo "检测到操作系统: $OS $VER"

# 安装OpenResty
install_openresty() {
    echo "=== 安装OpenResty ==="
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y wget gnupg ca-certificates
        
        # 添加OpenResty仓库
        wget -O - https://openresty.org/package/pubkey.gpg | apt-key add -
        echo "deb http://openresty.org/package/ubuntu $(lsb_release -sc) main" \
            > /etc/apt/sources.list.d/openresty.list
        
        apt-get update
        apt-get install -y openresty
        
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        # CentOS/RHEL
        yum install -y wget
        
        # 添加OpenResty仓库
        wget https://openresty.org/package/centos/openresty.repo -O /etc/yum.repos.d/openresty.repo
        yum install -y openresty
        
    else
        echo "不支持的操作系统: $OS"
        exit 1
    fi
    
    echo "OpenResty安装完成"
}

# 安装Redis
install_redis() {
    echo "=== 安装Redis ==="
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        apt-get install -y redis-server
        systemctl enable redis-server
        systemctl start redis-server
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        yum install -y redis
        systemctl enable redis
        systemctl start redis
    fi
    
    echo "Redis安装完成"
}

# 部署配置文件
deploy_configs() {
    echo "=== 部署配置文件 ==="
    
    # 创建目录
    mkdir -p /etc/nginx/lua
    mkdir -p /var/log/nginx
    
    # 复制Lua脚本
    cp init.lua /etc/nginx/lua/
    cp cc_defense.lua /etc/nginx/lua/
    
    # 设置权限
    chown -R nginx:nginx /etc/nginx/lua 2>/dev/null || chown -R www-data:www-data /etc/nginx/lua
    chmod 644 /etc/nginx/lua/*.lua
    
    # 备份原配置
    if [ -f /etc/nginx/nginx.conf ]; then
        cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
        echo "原nginx配置已备份"
    fi
    
    # 复制新配置 (需要手动调整)
    echo "请手动将nginx.conf的内容合并到 /etc/nginx/nginx.conf"
    echo "或者根据实际情况调整配置文件"
    
    echo "配置文件部署完成"
}

# 测试配置
test_config() {
    echo "=== 测试配置 ==="
    
    # 测试Redis连接
    if redis-cli ping > /dev/null 2>&1; then
        echo "✓ Redis连接正常"
    else
        echo "✗ Redis连接失败"
        exit 1
    fi
    
    # 测试nginx配置
    if nginx -t > /dev/null 2>&1; then
        echo "✓ Nginx配置正确"
    else
        echo "✗ Nginx配置有误，请检查"
        nginx -t
        exit 1
    fi
    
    echo "配置测试通过"
}

# 启动服务
start_services() {
    echo "=== 启动服务 ==="
    
    # 启动Redis
    systemctl start redis 2>/dev/null || systemctl start redis-server
    systemctl enable redis 2>/dev/null || systemctl enable redis-server
    
    # 重载nginx
    systemctl reload nginx 2>/dev/null || nginx -s reload
    systemctl enable nginx
    
    echo "服务启动完成"
}

# 显示状态
show_status() {
    echo "=== 服务状态 ==="
    
    echo "Redis状态:"
    systemctl status redis --no-pager -l 2>/dev/null || systemctl status redis-server --no-pager -l
    
    echo -e "\nNginx状态:"
    systemctl status nginx --no-pager -l
    
    echo -e "\n=== Redis数据检查 ==="
    echo "可信IP数量: $(redis-cli scard trusted_ips 2>/dev/null || echo '0')"
    echo "黑名单IP数量: $(redis-cli scard blacklist_ip 2>/dev/null || echo '0')"
}

# 主函数
main() {
    echo "开始部署..."
    
    # 检查文件是否存在
    for file in init.lua cc_defense.lua nginx.conf; do
        if [ ! -f "$file" ]; then
            echo "错误: 文件 $file 不存在"
            exit 1
        fi
    done
    
    # 询问是否继续
    read -p "是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    install_openresty
    install_redis
    deploy_configs
    test_config
    start_services
    show_status
    
    echo ""
    echo "=== 部署完成 ==="
    echo "请注意:"
    echo "1. 手动调整 /etc/nginx/nginx.conf 配置"
    echo "2. 根据实际情况修改 init.lua 中的URL规则"
    echo "3. 确保后端服务正确集成Redis操作"
    echo "4. 监控日志: tail -f /var/log/nginx/error.log"
    echo ""
    echo "测试命令:"
    echo "redis-cli scard trusted_ips  # 查看可信IP数量"
    echo "redis-cli smembers blacklist_ip  # 查看黑名单IP"
}

# 执行主函数
main "$@"
